{"name": "pv", "private": true, "version": "1.3.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.7.0", "@types/uuid": "^10.0.0", "antd": "^5.24.9", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "html2canvas": "^1.4.1", "i18next": "^25.1.1", "jspdf": "^3.0.1", "lz-string": "^1.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "redux-persist": "^6.0.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}